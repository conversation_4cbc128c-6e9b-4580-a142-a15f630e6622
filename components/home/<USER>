interface HeaderProps {
  coupleNames?: string
  className?: string
}

export default function Header({ 
  coupleNames = "SEAN & EVA", 
  className = "" 
}: HeaderProps) {
  return (
    <div className={`text-center mb-8 ${className}`}>
      <div className="flex justify-center items-center mb-4">
        <div className="text-white text-lg font-light tracking-wider">
          {coupleNames}
        </div>
      </div>

      {/* City Skyline */}
      <div className="flex justify-center mb-6">
        <svg 
          width="200" 
          height="40" 
          viewBox="0 0 200 40" 
          className="text-white fill-current opacity-60"
        >
          <rect x="10" y="20" width="8" height="20" />
          <rect x="20" y="15" width="6" height="25" />
          <rect x="28" y="25" width="4" height="15" />
          <rect x="35" y="10" width="10" height="30" />
          <rect x="48" y="18" width="6" height="22" />
          <rect x="58" y="12" width="8" height="28" />
          <rect x="70" y="22" width="5" height="18" />
          <rect x="80" y="8" width="12" height="32" />
          <rect x="95" y="16" width="7" height="24" />
          <rect x="105" y="20" width="6" height="20" />
          <rect x="115" y="14" width="9" height="26" />
          <rect x="128" y="18" width="5" height="22" />
          <rect x="138" y="12" width="8" height="28" />
          <rect x="150" y="24" width="6" height="16" />
          <rect x="160" y="16" width="7" height="24" />
          <rect x="170" y="20" width="5" height="20" />
          <rect x="180" y="18" width="8" height="22" />
        </svg>
      </div>
    </div>
  )
}
